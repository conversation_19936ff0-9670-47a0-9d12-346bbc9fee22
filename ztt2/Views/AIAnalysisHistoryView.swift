//
//  AIAnalysisHistoryView.swift
//  ztt2
//
//  Created by Augment Agent on 2025/8/1.
//

import SwiftUI

/**
 * AI分析历史记录页面
 * 显示成员的所有AI分析报告历史
 */
struct AIAnalysisHistoryView: View {
    
    // MARK: - Properties
    
    let member: Member
    @Environment(\.dismiss) private var dismiss
    @StateObject private var viewModel = AIAnalysisViewModel()
    @State private var selectedReportType: AIReportType? = nil
    @State private var selectedReport: AIReport? = nil
    @State private var showingReportDetail = false
    @State private var isReportReady = false
    
    // MARK: - Body
    
    var body: some View {
        GeometryReader { geometry in
            ZStack {
                // 背景渐变 - 与AI分析页面保持一致
                createBackgroundGradient()

                // 主要内容 - 使用ScrollView确保内容可滚动
                ScrollView {
                    VStack(spacing: 0) {
                        // 顶部间距，为导航栏留空间
                        Color.clear
                            .frame(height: max(geometry.safeAreaInsets.top, 44) + 60)

                        // 筛选器
                        filterSegmentedControl
                            .padding(.horizontal, 20)
                            .padding(.bottom, 20)

                        // 报告列表
                        if filteredReports.isEmpty {
                            emptyStateView
                        } else {
                            reportsList
                        }

                        // 底部安全区域
                        Color.clear
                            .frame(height: geometry.safeAreaInsets.bottom + 20)
                    }
                }

                // 顶部导航栏 - 浮动在最上层
                VStack {
                    topNavigationBar
                        .padding(.top, max(geometry.safeAreaInsets.top, 44))
                    Spacer()
                }
            }
        }
        .navigationBarHidden(true)
        .ignoresSafeArea()
        .fullScreenCover(isPresented: $showingReportDetail) {
            if let report = selectedReport, isReportReady {
                AIReportDetailCoreDataView(aiReport: report)
                    .onAppear {
                        print("🔍 [AIAnalysisHistory] ReportDetailView已显示")
                        print("🔍 [AIAnalysisHistory] 报告标题: \(report.reportTypeDisplayName)")
                        print("🔍 [AIAnalysisHistory] 报告内容长度: \(report.content?.count ?? 0)")
                    }
            } else {
                VStack {
                    ProgressView("加载中...")
                        .padding()
                    Text("正在准备报告内容")
                        .foregroundColor(.secondary)
                        .font(.caption)
                }
                .onAppear {
                    print("❌ [AIAnalysisHistory] 报告显示失败 - selectedReport: \(selectedReport != nil ? "存在" : "nil"), isReportReady: \(isReportReady)")
                    // 如果数据还没准备好，尝试重新触发
                    if selectedReport != nil && !isReportReady {
                        DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
                            isReportReady = true
                        }
                    }
                }
            }
        }
        .onAppear {
            loadReports()
        }
        .onChange(of: showingReportDetail) { isShowing in
            if !isShowing {
                // 详情页关闭时重置状态
                print("🔍 [AIAnalysisHistory] 详情页已关闭，重置状态")
                selectedReport = nil
                isReportReady = false
            }
        }
    }
    
    // MARK: - View Components

    /**
     * 创建背景渐变 - 与AI分析页面保持一致
     */
    private func createBackgroundGradient() -> some View {
        LinearGradient(
            gradient: Gradient(stops: [
                .init(color: Color(hex: "#fcfff4"), location: 0.0),
                .init(color: Color(hex: "#f8fdf0"), location: 0.3),
                .init(color: Color.white, location: 0.7),
                .init(color: Color(hex: "#fafffe"), location: 1.0)
            ]),
            startPoint: .top,
            endPoint: .bottom
        )
        .ignoresSafeArea()
    }

    /**
     * 顶部导航栏 - 与AI分析页面保持一致的样式
     */
    private var topNavigationBar: some View {
        HStack {
            // 返回按钮
            Button(action: {
                dismiss()
            }) {
                HStack(spacing: 6) {
                    Image(systemName: "chevron.left")
                        .font(.system(size: 16, weight: .medium))
                        .foregroundColor(DesignSystem.Colors.textPrimary)
                    Text("返回")
                        .font(.system(size: 16, weight: .medium))
                        .foregroundColor(DesignSystem.Colors.textPrimary)
                }
                .padding(.horizontal, 12)
                .padding(.vertical, 8)
                .background(
                    RoundedRectangle(cornerRadius: 20)
                        .fill(Color.white.opacity(0.9))
                        .shadow(color: .black.opacity(0.1), radius: 4, x: 0, y: 2)
                )
            }

            Spacer()

            // 标题
            Text("历史分析记录")
                .font(.system(size: 18, weight: .semibold))
                .foregroundColor(DesignSystem.Colors.textPrimary)

            Spacer()

            // 占位符保持平衡
            HStack(spacing: 6) {
                Image(systemName: "chevron.left")
                    .font(.system(size: 16, weight: .medium))
                Text("返回")
                    .font(.system(size: 16, weight: .medium))
            }
            .padding(.horizontal, 12)
            .padding(.vertical, 8)
            .opacity(0)
        }
        .padding(.horizontal, 20)
        .padding(.vertical, 12)
    }

    /// 筛选器分段控制 - 优化样式与AI分析页面保持一致
    private var filterSegmentedControl: some View {
        HStack(spacing: 0) {
            ForEach([nil, AIReportType.behaviorAnalysis, AIReportType.growthReport], id: \.self) { type in
                Button(action: {
                    selectedReportType = type
                    loadReports()
                }) {
                    VStack(spacing: 0) {
                        Text(type?.displayName ?? "全部")
                            .font(.system(size: 14, weight: .medium))
                            .foregroundColor(selectedReportType == type ? DesignSystem.Colors.textPrimary : DesignSystem.Colors.textSecondary)
                            .frame(maxWidth: .infinity)
                            .padding(.vertical, 12)
                            .background(
                                RoundedRectangle(cornerRadius: 16)
                                    .fill(selectedReportType == type ? Color.white : Color.clear)
                                    .shadow(color: selectedReportType == type ? .black.opacity(0.1) : .clear, radius: 2, x: 0, y: 1)
                            )

                        // 选中状态下显示绿色底线
                        if selectedReportType == type {
                            Rectangle()
                                .fill(DesignSystem.Colors.primary)
                                .frame(width: 70, height: 3)
                                .cornerRadius(1.5)
                        }
                    }
                }
            }
        }
        .padding(.horizontal, 16)
        .padding(.vertical, 8)
        .background(
            RoundedRectangle(cornerRadius: 20)
                .fill(Color.white.opacity(0.8))
                .shadow(color: .black.opacity(0.05), radius: 8, x: 0, y: 4)
        )
    }
    
    /// 报告列表 - 优化样式
    private var reportsList: some View {
        LazyVStack(spacing: 16) {
            ForEach(groupedReports.keys.sorted(by: >), id: \.self) { date in
                VStack(alignment: .leading, spacing: 12) {
                    // 日期标题
                    HStack {
                        Text(formatSectionDate(date))
                            .font(.system(size: 16, weight: .semibold))
                            .foregroundColor(DesignSystem.Colors.textPrimary)
                        Spacer()
                    }
                    .padding(.horizontal, 20)

                    // 该日期的报告列表
                    VStack(spacing: 8) {
                        ForEach(groupedReports[date] ?? [], id: \.id) { report in
                            reportRowView(report)
                                .onTapGesture {
                                    print("🔍 [AIAnalysisHistory] 用户点击报告 - 类型: \(report.reportTypeDisplayName), ID: \(report.id?.uuidString ?? "无ID")")
                                    print("🔍 [AIAnalysisHistory] 报告内容长度: \(report.content?.count ?? 0)")

                                    // 确保状态重置和正确设置
                                    isReportReady = false
                                    selectedReport = nil

                                    // 使用异步方式确保状态更新完成
                                    DispatchQueue.main.async {
                                        selectedReport = report
                                        isReportReady = true

                                        // 延迟一个runloop确保状态完全更新
                                        DispatchQueue.main.async {
                                            showingReportDetail = true
                                            print("🔍 [AIAnalysisHistory] 状态设置完成，显示详情页")
                                        }
                                    }
                                }
                        }
                    }
                }
            }
        }
        .padding(.horizontal, 20)
    }
    
    /// 报告行视图 - 优化卡片样式
    private func reportRowView(_ report: AIReport) -> some View {
        HStack(spacing: 16) {
            // 报告类型图标容器
            ZStack {
                Circle()
                    .fill(
                        LinearGradient(
                            gradient: Gradient(colors: [
                                reportTypeColor(for: report).opacity(0.2),
                                reportTypeColor(for: report).opacity(0.1)
                            ]),
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        )
                    )
                    .frame(width: 50, height: 50)
                    .shadow(color: reportTypeColor(for: report).opacity(0.2), radius: 4, x: 0, y: 2)

                Image(systemName: reportTypeIcon(for: report))
                    .font(.system(size: 20, weight: .medium))
                    .foregroundColor(reportTypeColor(for: report))
            }

            // 报告信息
            VStack(alignment: .leading, spacing: 6) {
                Text(report.reportTypeDisplayName)
                    .font(.system(size: 16, weight: .semibold))
                    .foregroundColor(DesignSystem.Colors.textPrimary)

                if let summary = report.inputDataSummary {
                    Text(summary)
                        .font(.system(size: 14))
                        .foregroundColor(DesignSystem.Colors.textSecondary)
                        .lineLimit(2)
                }

                Text(report.formattedCreatedAt)
                    .font(.system(size: 12))
                    .foregroundColor(DesignSystem.Colors.textTertiary)
            }

            Spacer()

            // 箭头指示器
            Image(systemName: "chevron.right")
                .font(.system(size: 14, weight: .medium))
                .foregroundColor(DesignSystem.Colors.textTertiary)
        }
        .padding(16)
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(
                    LinearGradient(
                        gradient: Gradient(colors: [
                            Color.white,
                            Color(hex: "#fcfff4").opacity(0.3)
                        ]),
                        startPoint: .topLeading,
                        endPoint: .bottomTrailing
                    )
                )
                .shadow(color: .black.opacity(0.05), radius: 8, x: 0, y: 4)
                .shadow(color: DesignSystem.Colors.primary.opacity(0.1), radius: 2, x: 0, y: 1)
        )
        .contentShape(Rectangle())
    }
    
    /// 空状态视图 - 优化样式与AI分析页面保持一致
    private var emptyStateView: some View {
        VStack(spacing: 32) {
            Spacer()

            // 图标容器
            ZStack {
                Circle()
                    .fill(
                        LinearGradient(
                            gradient: Gradient(colors: [
                                DesignSystem.Colors.primary.opacity(0.1),
                                DesignSystem.Colors.primary.opacity(0.05)
                            ]),
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        )
                    )
                    .frame(width: 120, height: 120)
                    .shadow(color: DesignSystem.Colors.primary.opacity(0.1), radius: 20, x: 0, y: 8)

                Image(systemName: "doc.text.magnifyingglass")
                    .font(.system(size: 50, weight: .light))
                    .foregroundColor(DesignSystem.Colors.primary)
            }

            // 文本内容
            VStack(spacing: 12) {
                Text("暂无历史报告")
                    .font(.system(size: 20, weight: .semibold))
                    .foregroundColor(DesignSystem.Colors.textPrimary)

                Text(emptyStateMessage)
                    .font(.system(size: 16))
                    .foregroundColor(DesignSystem.Colors.textSecondary)
                    .multilineTextAlignment(.center)
                    .lineSpacing(4)
                    .padding(.horizontal, 40)
            }

            Spacer()
        }
        .padding(.horizontal, 32)
    }
    
    // MARK: - Computed Properties
    
    /// 筛选后的报告
    private var filteredReports: [AIReport] {
        if let selectedType = selectedReportType {
            let typeString = selectedType == .behaviorAnalysis ? "analysis" : "growth"
            return viewModel.historyReports.filter { ($0.reportType ?? "analysis") == typeString }
        } else {
            return viewModel.historyReports
        }
    }
    
    /// 按日期分组的报告
    private var groupedReports: [Date: [AIReport]] {
        Dictionary(grouping: filteredReports) { report in
            Calendar.current.startOfDay(for: report.createdAt ?? Date())
        }
    }
    
    /// 空状态消息
    private var emptyStateMessage: String {
        if let selectedType = selectedReportType {
            return "还没有生成过\(selectedType.displayName)\n快去生成第一份报告吧！"
        } else {
            return "还没有生成过AI分析报告\n快去生成第一份报告吧！"
        }
    }
    
    // MARK: - Helper Methods
    
    /// 加载报告
    private func loadReports() {
        viewModel.loadHistoryReports(for: member, reportType: selectedReportType)
    }
    
    /// 获取报告类型图标
    private func reportTypeIcon(for report: AIReport) -> String {
        switch report.reportType ?? "analysis" {
        case "analysis":
            return "chart.line.uptrend.xyaxis"
        case "growth":
            return "heart.text.square"
        default:
            return "doc.text"
        }
    }
    
    /// 获取报告类型颜色
    private func reportTypeColor(for report: AIReport) -> Color {
        switch report.reportType ?? "analysis" {
        case "analysis":
            return .blue
        case "growth":
            return .green
        default:
            return .gray
        }
    }
    
    /// 格式化分组日期
    private func formatSectionDate(_ date: Date) -> String {
        let formatter = DateFormatter()
        formatter.locale = Locale(identifier: "zh_CN")
        
        let calendar = Calendar.current
        if calendar.isDateInToday(date) {
            return "今天"
        } else if calendar.isDateInYesterday(date) {
            return "昨天"
        } else if calendar.isDate(date, equalTo: Date(), toGranularity: .year) {
            formatter.dateFormat = "M月d日"
            return formatter.string(from: date)
        } else {
            formatter.dateFormat = "yyyy年M月d日"
            return formatter.string(from: date)
        }
    }
}

// MARK: - Preview

#if DEBUG
struct AIAnalysisHistoryView_Previews: PreviewProvider {
    static var previews: some View {
        // 创建一个示例成员用于预览
        let context = PersistenceController.preview.container.viewContext
        let member = Member(context: context)
        member.name = "小明"
        member.role = "son"
        member.birthDate = Calendar.current.date(byAdding: .year, value: -8, to: Date())
        
        return AIAnalysisHistoryView(member: member)
    }
}
#endif
