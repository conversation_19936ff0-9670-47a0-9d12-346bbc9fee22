//
//  AIAnalysisViewModel.swift
//  ztt2
//
//  Created by Augment Agent on 2025/8/1.
//

import Foundation
import SwiftUI
import Combine

/**
 * AI分析视图模型
 * 管理AI分析功能的状态和业务逻辑
 */
@MainActor
class AIAnalysisViewModel: ObservableObject {
    
    // MARK: - Published Properties
    
    /// 是否正在生成报告
    @Published var isGenerating = false
    
    /// 错误信息
    @Published var errorMessage: String?
    
    /// 成功信息
    @Published var successMessage: String?
    
    /// 当前生成的报告
    @Published var currentReport: AIAnalysisReport?
    
    /// 历史报告列表
    @Published var historyReports: [AIReport] = []
    
    /// 是否显示历史报告
    @Published var showingHistoryReports = false
    
    /// 是否显示权限升级提示
    @Published var showingUpgradePrompt = false
    
    /// 网络状态
    @Published var isNetworkAvailable = true
    
    // MARK: - Private Properties
    
    private let aiAnalysisService = AIAnalysisService()
    private let dataManager = DataManager.shared
    private var cancellables = Set<AnyCancellable>()
    
    // MARK: - Initialization
    
    init() {
        setupBindings()
    }
    
    // MARK: - Public Methods
    
    /**
     * 生成行为分析报告
     */
    func generateBehaviorAnalysisReport(for member: Member) async {
        await generateReport(for: member, reportType: .behaviorAnalysis)
    }
    
    /**
     * 生成成长报告
     */
    func generateGrowthReport(for member: Member) async {
        await generateReport(for: member, reportType: .growthReport)
    }
    
    /**
     * 检查是否可以生成报告
     */
    func canGenerateReport(for member: Member, reportType: AIReportType) -> (canGenerate: Bool, reason: String?) {
        let permissionResult = aiAnalysisService.validatePermissions(for: member, reportType: reportType)
        
        if permissionResult.isAllowed {
            return (true, nil)
        } else {
            return (false, permissionResult.errorMessage)
        }
    }
    
    /**
     * 获取今日剩余使用次数
     */
    func getRemainingUsage(for member: Member, reportType: AIReportType) -> Int {
        let usage = aiAnalysisService.getTodayUsage(for: member)
        return usage.remainingCount(for: reportType)
    }
    
    /**
     * 加载历史报告
     */
    func loadHistoryReports(for member: Member, reportType: AIReportType? = nil) {
        historyReports = aiAnalysisService.getHistoryReports(for: member, reportType: reportType)
    }
    
    /**
     * 清除错误信息
     */
    func clearError() {
        errorMessage = nil
    }
    
    /**
     * 清除成功信息
     */
    func clearSuccess() {
        successMessage = nil
    }
    
    /**
     * 检查网络连接
     */
    func checkNetworkConnection() -> Bool {
        return aiAnalysisService.checkNetworkConnection()
    }
    
    // MARK: - Private Methods
    
    /**
     * 设置数据绑定
     */
    private func setupBindings() {
        // 监听AI服务的状态变化
        aiAnalysisService.$isGenerating
            .receive(on: DispatchQueue.main)
            .assign(to: \.isGenerating, on: self)
            .store(in: &cancellables)
        
        aiAnalysisService.$errorMessage
            .receive(on: DispatchQueue.main)
            .assign(to: \.errorMessage, on: self)
            .store(in: &cancellables)
        
        aiAnalysisService.$isNetworkAvailable
            .receive(on: DispatchQueue.main)
            .assign(to: \.isNetworkAvailable, on: self)
            .store(in: &cancellables)
    }
    
    /**
     * 生成报告的通用方法
     */
    private func generateReport(for member: Member, reportType: AIReportType) async {
        // 清除之前的消息
        clearError()
        clearSuccess()
        currentReport = nil
        
        // 检查权限
        let permissionResult = aiAnalysisService.validatePermissions(for: member, reportType: reportType)
        guard permissionResult.isAllowed else {
            if permissionResult.canUpgrade {
                showingUpgradePrompt = true
            }
            errorMessage = permissionResult.errorMessage
            return
        }
        
        // 检查网络连接
        guard checkNetworkConnection() else {
            errorMessage = "网络连接不可用，请检查网络设置后重试"
            return
        }
        
        do {
            // 生成报告
            let report = try await aiAnalysisService.generateReport(for: member, reportType: reportType)
            
            // 更新UI
            currentReport = report
            successMessage = "\(reportType.displayName)生成成功！"
            
            // 刷新历史报告
            loadHistoryReports(for: member, reportType: reportType)
            
        } catch {
            // 处理错误
            handleGenerationError(error)
        }
    }
    
    /**
     * 处理生成报告时的错误
     */
    private func handleGenerationError(_ error: Error) {
        if let aiError = error as? AIAnalysisError {
            switch aiError {
            case .permissionDenied:
                showingUpgradePrompt = true
                errorMessage = "需要高级会员权限才能使用AI分析功能"
            case .rateLimitExceeded:
                errorMessage = "今日分析次数已用完，请明天再试"
            case .networkError:
                errorMessage = "网络连接失败，请检查网络设置"
            case .apiKeyMissing, .apiKeyInvalid:
                errorMessage = "API配置错误，请联系客服"
            case .insufficientData:
                errorMessage = "数据不足，需要至少10条记录才能生成分析报告"
            default:
                errorMessage = aiError.localizedDescription
            }
        } else {
            errorMessage = "生成报告失败：\(error.localizedDescription)"
        }
    }
}

// MARK: - Helper Extensions

extension AIAnalysisViewModel {
    
    /**
     * 获取报告类型的使用情况描述
     */
    func getUsageDescription(for member: Member, reportType: AIReportType) -> String {
        let remaining = getRemainingUsage(for: member, reportType: reportType)
        return "今日剩余：\(remaining)/2次"
    }
    
    /**
     * 检查是否可以显示AI分析按钮
     */
    func shouldShowAIAnalysisButton(for member: Member) -> Bool {
        return member.isChild
    }
    
    /**
     * 获取权限检查结果的用户友好描述
     */
    func getPermissionDescription(for member: Member, reportType: AIReportType) -> String {
        let result = canGenerateReport(for: member, reportType: reportType)
        
        if result.canGenerate {
            return getUsageDescription(for: member, reportType: reportType)
        } else {
            return result.reason ?? "无法生成报告"
        }
    }
}
